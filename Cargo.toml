[package]
name = "clia-config-expr"
version = "0.1.0"
edition = "2021"
description = "A flexible configuration expression evaluator with JSON schema support"
license = "MIT OR Apache-2.0"
repository = "https://github.com/clia/config-expr"
keywords = ["config", "rules", "expression", "json", "evaluator"]
categories = ["config", "parsing"]

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"

[dependencies]
regex = "1.11.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
thiserror = "2.0.12"
